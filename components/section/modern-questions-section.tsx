'use client';

import { useState } from 'react';
import {
  ChevronDown,
  ChevronRight,
  Code,
  FileText,
  MessageSquare,
  Play,
  Video,
  CheckCircle,
  XCircle,
  Clock,
  Target,
} from 'lucide-react';
import ReactMarkdown from 'react-markdown';

import { Badge } from '@camped-ui/badge';
import { Button } from '@camped-ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@camped-ui/card';
import { cn } from '@camped-ui/lib';
import { Separator } from '@camped-ui/separator';

import { DetailScoreSection, FeedBackSection, PerformanceScoreSection } from './index';
import { CodingResultStatusCard } from '../cards/coding-result-status-card';
import { InterviewQuestionTiming } from '../cards/admin/interview-question-timing';
import VideoPlayer from '../ui/video-player';

interface ModernQuestionsSectionProps {
  questions: any[];
  careerPractice?: any;
  userId?: string;
}

// Helper function to get question type icon and color
const getQuestionTypeInfo = (round: string) => {
  const typeMap = {
    'video-interview': {
      icon: Video,
      color: 'text-purple-600 bg-purple-100 dark:bg-purple-900/20',
      borderColor: 'border-purple-200 dark:border-purple-800',
      label: 'Video Interview',
    },
    'coding-interview': {
      icon: Code,
      color: 'text-blue-600 bg-blue-100 dark:bg-blue-900/20',
      borderColor: 'border-blue-200 dark:border-blue-800',
      label: 'Coding Challenge',
    },
    'frontend-interview': {
      icon: Code,
      color: 'text-cyan-600 bg-cyan-100 dark:bg-cyan-900/20',
      borderColor: 'border-cyan-200 dark:border-cyan-800',
      label: 'Frontend Challenge',
    },
    'multiple-choice': {
      icon: Target,
      color: 'text-green-600 bg-green-100 dark:bg-green-900/20',
      borderColor: 'border-green-200 dark:border-green-800',
      label: 'Multiple Choice',
    },
    'written-interview': {
      icon: FileText,
      color: 'text-orange-600 bg-orange-100 dark:bg-orange-900/20',
      borderColor: 'border-orange-200 dark:border-orange-800',
      label: 'Written Question',
    },
  };

  return typeMap[round] || {
    icon: MessageSquare,
    color: 'text-gray-600 bg-gray-100 dark:bg-gray-900/20',
    borderColor: 'border-gray-200 dark:border-gray-800',
    label: 'Question',
  };
};

// Helper function to capitalize first letter
const capitalizeFirstLetter = (string: string) => {
  return string?.charAt(0)?.toUpperCase() + string?.slice(1);
};

// Individual Question Card Component
const QuestionCard = ({ 
  question, 
  index, 
  userId, 
  isExpanded, 
  onToggle 
}: { 
  question: any; 
  index: number; 
  userId?: string;
  isExpanded: boolean;
  onToggle: () => void;
}) => {
  const [videoUrl, setVideoUrl] = useState('');
  
  const typeInfo = getQuestionTypeInfo(question?.round);
  const IconComponent = typeInfo.icon;
  
  const isAnswered = question?.isAnswered || 
    question?.answer || 
    question?.status || 
    question?.memory || 
    question?.time;

  return (
    <Card className={cn('transition-all duration-200', typeInfo.borderColor)}>
      {/* Question Header - Always Visible */}
      <CardHeader 
        className="cursor-pointer pb-3"
        onClick={onToggle}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className={cn('rounded-lg p-2', typeInfo.color)}>
              <IconComponent className="h-5 w-5" />
            </div>
            <div className="flex flex-col gap-1">
              <div className="flex items-center gap-2">
                <CardTitle className="text-lg">
                  Question {index + 1}
                </CardTitle>
                <Badge variant="outline" className="text-xs">
                  {typeInfo.label}
                </Badge>
                {isAnswered ? (
                  <CheckCircle className="h-4 w-4 text-green-600" />
                ) : (
                  <XCircle className="h-4 w-4 text-red-500" />
                )}
              </div>
              {question?.question && (
                <CardDescription className="line-clamp-2">
                  {question.question}
                </CardDescription>
              )}
            </div>
          </div>
          <Button variant="ghost" size="sm">
            {isExpanded ? (
              <ChevronDown className="h-4 w-4" />
            ) : (
              <ChevronRight className="h-4 w-4" />
            )}
          </Button>
        </div>
      </CardHeader>

      {/* Expandable Content */}
      {isExpanded && (
        <CardContent className="space-y-6">
          {/* Question Content */}
          {question?.question && (
            <div>
              <h3 className="mb-2 text-sm font-semibold text-gray-700 dark:text-gray-300">
                Question
              </h3>
              <CardDescription className="text-sm">
                {question.question}
              </CardDescription>
            </div>
          )}

          {/* Question Description */}
          {question?.questionDescription && (
            <div>
              <h3 className="mb-2 text-sm font-semibold text-gray-700 dark:text-gray-300">
                Description
              </h3>
              {question?.round === 'frontend-interview' ? (
                <CardDescription className="prose prose-sm dark:prose-invert">
                  <ReactMarkdown>
                    {question.questionDescription.replace(/\\n/g, '\n')}
                  </ReactMarkdown>
                </CardDescription>
              ) : (
                <CardDescription className="text-sm">
                  {question.questionDescription}
                </CardDescription>
              )}
            </div>
          )}

          {/* Answer/Response Section */}
          {isAnswered ? (
            <div className="space-y-4">
              {/* Video Response */}
              {question?.round === 'video-interview' && (
                <div>
                  <h3 className="mb-2 text-sm font-semibold text-gray-700 dark:text-gray-300">
                    Video Response
                  </h3>
                  {videoUrl ? (
                    <VideoPlayer src={videoUrl} />
                  ) : (
                    <div className="flex h-48 items-center justify-center rounded-lg border bg-gray-50 dark:bg-gray-900">
                      <div className="text-center">
                        <Play className="mx-auto mb-2 h-8 w-8 text-gray-400" />
                        <p className="text-sm text-gray-500">Video response available</p>
                      </div>
                    </div>
                  )}
                  {question?.isAnswered && (
                    <InterviewQuestionTiming questions={[question]} activeIndex={0} />
                  )}
                </div>
              )}

              {/* Text Answer/Transcript */}
              {question?.answer && (
                <div>
                  <h3 className="mb-2 text-sm font-semibold text-gray-700 dark:text-gray-300">
                    Transcript
                  </h3>
                  <Card className="bg-gray-50 dark:bg-gray-900/50">
                    <CardContent className="p-4">
                      <CardDescription className="text-sm">
                        {question.answer}
                      </CardDescription>
                    </CardContent>
                  </Card>
                </div>
              )}

              {/* Coding Results */}
              {question?.round === 'coding-interview' && question?.status && (
                <div>
                  <h3 className="mb-2 text-sm font-semibold text-gray-700 dark:text-gray-300">
                    Execution Results
                  </h3>
                  <div className="grid grid-cols-2 gap-3 md:grid-cols-4">
                    <CodingResultStatusCard
                      title="Language"
                      description={capitalizeFirstLetter(question?.language || 'N/A')}
                    />
                    <CodingResultStatusCard
                      title="Time"
                      description={question?.time || '-'}
                    />
                    <CodingResultStatusCard
                      title="Memory"
                      description={question?.memory || '-'}
                    />
                    <CodingResultStatusCard
                      title="Status"
                      description={question?.status || '-'}
                    />
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="flex h-32 items-center justify-center rounded-lg border bg-gray-50 dark:bg-gray-900">
              <div className="text-center">
                <XCircle className="mx-auto mb-2 h-8 w-8 text-gray-400" />
                <p className="text-sm text-gray-500">Candidate didn't attempt this question</p>
              </div>
            </div>
          )}

          {/* Performance and Feedback */}
          {question?.feedback && (
            <div className="space-y-4">
              <Separator />
              <div>
                <h3 className="mb-4 text-sm font-semibold text-gray-700 dark:text-gray-300">
                  Assessment & Feedback
                </h3>
                <div className="space-y-4">
                  <PerformanceScoreSection data={question.feedback} />
                  
                  {question.feedback.short_summary && (
                    <FeedBackSection
                      short_summary={question.feedback.short_summary}
                      title="Overall Feedback"
                    />
                  )}
                  
                  {question.feedback.strengths && (
                    <FeedBackSection
                      short_summary={question.feedback.strengths}
                      title="Strengths"
                    />
                  )}
                  
                  {question.feedback.areas_of_improvement && (
                    <FeedBackSection
                      short_summary={question.feedback.areas_of_improvement}
                      title="Areas for Improvement"
                    />
                  )}
                </div>
              </div>
            </div>
          )}
        </CardContent>
      )}
    </Card>
  );
};

export const ModernQuestionsSection = ({ 
  questions, 
  careerPractice, 
  userId 
}: ModernQuestionsSectionProps) => {
  const [expandedQuestions, setExpandedQuestions] = useState<Set<number>>(new Set());
  const [expandAll, setExpandAll] = useState(false);

  const toggleQuestion = (index: number) => {
    const newExpanded = new Set(expandedQuestions);
    if (newExpanded.has(index)) {
      newExpanded.delete(index);
    } else {
      newExpanded.add(index);
    }
    setExpandedQuestions(newExpanded);
  };

  const toggleAll = () => {
    if (expandAll) {
      setExpandedQuestions(new Set());
    } else {
      setExpandedQuestions(new Set(questions.map((_, index) => index)));
    }
    setExpandAll(!expandAll);
  };

  if (!questions || questions.length === 0) {
    return (
      <Card className="bg-gray-50 dark:bg-gray-900/20">
        <CardContent className="p-8 text-center">
          <MessageSquare className="mx-auto mb-4 h-12 w-12 text-gray-400" />
          <h3 className="mb-2 text-lg font-medium text-gray-600 dark:text-gray-300">
            No Questions Available
          </h3>
          <p className="text-gray-500 dark:text-gray-400">
            No interview questions found for this session.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header with Expand/Collapse All */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <MessageSquare className="h-5 w-5 text-blue-600" />
          <h3 className="text-lg font-semibold">Interview Questions</h3>
          <Badge variant="outline">{questions.length} Questions</Badge>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={toggleAll}
          className="text-xs"
        >
          {expandAll ? 'Collapse All' : 'Expand All'}
        </Button>
      </div>

      {/* Questions List */}
      <div className="space-y-4">
        {questions.map((question, index) => (
          <QuestionCard
            key={index}
            question={question}
            index={index}
            userId={userId}
            isExpanded={expandedQuestions.has(index)}
            onToggle={() => toggleQuestion(index)}
          />
        ))}
      </div>
    </div>
  );
};
