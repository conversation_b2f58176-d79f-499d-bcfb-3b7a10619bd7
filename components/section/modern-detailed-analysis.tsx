'use client';

import {
  <PERSON><PERSON><PERSON><PERSON>gle,
  Award,
  Brain,
  Clock,
  Code,
  FileText,
  Lightbulb,
  MessageSquare,
  Shield,
  Target,
  TrendingUp,
  Users,
  Video,
} from 'lucide-react';

import { Badge } from '@camped-ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@camped-ui/card';
import { cn } from '@camped-ui/lib';
import { Separator } from '@camped-ui/separator';

import { DetailScoreSection } from './detail-score-section';
import { EditableDecisionCard } from './editable-decision-card';
import { ResumeSection } from './resume-section';

interface ModernDetailedAnalysisProps {
  careerPractice: any;
  userProfile?: any;
  userId?: string;
  reason?: string;
  flag?: string;
  setFlag?: (flag: string) => void;
  setReason?: (reason: string) => void;
  handleOnSave?: () => void;
}

// Executive Summary Grid Component - Consolidates all key feedback into one modern layout

const ExecutiveSummaryGrid = ({
  careerPractice,
  flag,
  reason,
}: {
  careerPractice: any;
  flag?: string;
  reason?: string;
}) => {
  const getBadgeVariant = (level: string) => {
    if (['minimal', 'hire']?.includes(level?.toLowerCase())) return 'default';
    if (['medium']?.includes(level?.toLowerCase())) return 'secondary';
    return 'destructive';
  };

  const getRecommendationColor = (decision: string) => {
    if (['hire']?.includes(decision?.toLowerCase())) return 'text-green-700 dark:text-green-500';
    if (['weak hire']?.includes(decision?.toLowerCase()))
      return 'text-orange-600 dark:text-orange-400';
    return 'text-red-600 dark:text-red-400';
  };

  return (
    <div className="grid gap-6 md:grid-cols-2">
      {/* Recommendation Reasoning */}
      {(careerPractice?.feedback?.overall_recommendation || reason) && (
        <Card className="border-purple-200 bg-purple-50/50 dark:border-purple-800 dark:bg-purple-900/20">
          <CardHeader className="pb-3">
            <div className="flex items-center gap-2">
              <Target className="h-5 w-5 text-purple-600" />
              <CardTitle className="text-lg text-purple-800 dark:text-purple-200">
                Recommendation Reasoning
              </CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <span className="text-xs font-medium text-purple-600">Decision:</span>
                <span
                  className={`text-sm font-semibold ${getRecommendationColor(
                    flag || careerPractice?.feedback?.overall_recommendation?.decision,
                  )}`}
                >
                  {flag || careerPractice?.feedback?.overall_recommendation?.decision || 'Pending'}
                </span>
              </div>
              <p className="text-sm leading-relaxed text-purple-700 dark:text-purple-300">
                {reason ||
                  (typeof careerPractice?.feedback?.overall_recommendation === 'string'
                    ? careerPractice.feedback.overall_recommendation
                    : careerPractice?.feedback?.overall_recommendation?.reason) ||
                  'No recommendation reasoning available'}
              </p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Key Highlights / Proctoring */}
      {careerPractice?.feedback?.candidate_legitimacy?.flag_level && (
        <Card className="border-blue-200 bg-blue-50/50 dark:border-blue-800 dark:bg-blue-900/20">
          <CardHeader className="pb-3">
            <div className="flex items-center gap-2">
              <Shield className="h-5 w-5 text-blue-600" />
              <CardTitle className="text-lg text-blue-800 dark:text-blue-200">Proctoring</CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <span className="text-xs font-medium text-blue-600">Proctoring Assessment:</span>
                <Badge
                  variant={getBadgeVariant(careerPractice.feedback.candidate_legitimacy.flag_level)}
                >
                  {careerPractice.feedback.candidate_legitimacy.flag_level}
                </Badge>
              </div>
              <p className="text-sm leading-relaxed text-blue-700 dark:text-blue-300">
                {careerPractice.feedback.candidate_legitimacy.reason}
              </p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Key Strengths */}
      {careerPractice?.feedback?.strengths && (
        <Card className="border-green-200 bg-green-50/50 dark:border-green-800 dark:bg-green-900/20">
          <CardHeader className="pb-3">
            <div className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-green-600" />
              <CardTitle className="text-lg text-green-800 dark:text-green-200">
                Key Strengths
              </CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            <p className="text-sm leading-relaxed text-green-700 dark:text-green-300">
              {typeof careerPractice.feedback.strengths === 'string'
                ? careerPractice.feedback.strengths
                : careerPractice.feedback.strengths?.feedback || 'No strengths data available'}
            </p>
          </CardContent>
        </Card>
      )}

      {/* Areas for Improvement */}
      {careerPractice?.feedback?.areas_for_improvement && (
        <Card className="border-orange-200 bg-orange-50/50 dark:border-orange-800 dark:bg-orange-900/20">
          <CardHeader className="pb-3">
            <div className="flex items-center gap-2">
              <Lightbulb className="h-5 w-5 text-orange-600" />
              <CardTitle className="text-lg text-orange-800 dark:text-orange-200">
                Areas for Improvement
              </CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            <p className="text-sm leading-relaxed text-orange-700 dark:text-orange-300">
              {typeof careerPractice.feedback.areas_for_improvement === 'string'
                ? careerPractice.feedback.areas_for_improvement
                : careerPractice.feedback.areas_for_improvement?.feedback ||
                  'No improvement areas identified'}
            </p>
          </CardContent>
        </Card>
      )}

      {/* Job Fit Analysis Summary */}
      {careerPractice?.feedback?.job_fit?.Overall_score && (
        <Card className="border-teal-200 bg-teal-50/50 dark:border-teal-800 dark:bg-teal-900/20">
          <CardHeader className="pb-3">
            <div className="flex items-center gap-2">
              <Award className="h-5 w-5 text-teal-600" />
              <CardTitle className="text-lg text-teal-800 dark:text-teal-200">
                Job Fit Analysis
              </CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <span className="text-xs font-medium text-teal-600">Overall Score:</span>
                <Badge variant="secondary">
                  {careerPractice.feedback.job_fit.Overall_score.score}/100
                </Badge>
              </div>
              <p className="text-sm leading-relaxed text-teal-700 dark:text-teal-300">
                {careerPractice.feedback.job_fit.Overall_score.feedback ||
                  'Job fit analysis completed'}
              </p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Core Values Assessment Summary */}
      {careerPractice?.feedback?.coreValue?.Overall_fit && (
        <Card className="border-indigo-200 bg-indigo-50/50 dark:border-indigo-800 dark:bg-indigo-900/20">
          <CardHeader className="pb-3">
            <div className="flex items-center gap-2">
              <Users className="h-5 w-5 text-indigo-600" />
              <CardTitle className="text-lg text-indigo-800 dark:text-indigo-200">
                Core Values
              </CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <span className="text-xs font-medium text-indigo-600">Cultural Fit:</span>
                <Badge
                  variant={
                    careerPractice.feedback.coreValue.Overall_fit.decision === 'fit'
                      ? 'default'
                      : 'destructive'
                  }
                >
                  {careerPractice.feedback.coreValue.Overall_fit.decision}
                </Badge>
              </div>
              <p className="text-sm leading-relaxed text-indigo-700 dark:text-indigo-300">
                {careerPractice.feedback.coreValue.Overall_fit.reason ||
                  'Core values assessment completed'}
              </p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export const ModernDetailedAnalysis = ({
  careerPractice,
  userProfile,
  userId,
  reason,
  flag,
  setFlag,
  setReason,
  handleOnSave,
}: ModernDetailedAnalysisProps) => {
  const getSectionIcon = (section: string) => {
    const icons = {
      resume: FileText,
      timing: Clock,
      proctoring: Shield,
      recommendations: Target,
      coreValues: Users,
      jobFit: Award,
      feedback: MessageSquare,
      comments: MessageSquare,
      recordings: Video,
      questions: Code,
      strengths: TrendingUp,
      improvements: Lightbulb,
    };
    const Icon = icons[section] || FileText;
    return <Icon className="h-5 w-5" />;
  };

  const getSectionColor = (section: string) => {
    const colors = {
      resume: 'text-blue-600 bg-blue-100 dark:bg-blue-900/20',
      timing: 'text-green-600 bg-green-100 dark:bg-green-900/20',
      proctoring: 'text-red-600 bg-red-100 dark:bg-red-900/20',
      recommendations: 'text-purple-600 bg-purple-100 dark:bg-purple-900/20',
      coreValues: 'text-indigo-600 bg-indigo-100 dark:bg-indigo-900/20',
      jobFit: 'text-teal-600 bg-teal-100 dark:bg-teal-900/20',
      feedback: 'text-orange-600 bg-orange-100 dark:bg-orange-900/20',
      comments: 'text-gray-600 bg-gray-100 dark:bg-gray-900/20',
      recordings: 'text-pink-600 bg-pink-100 dark:bg-pink-900/20',
      questions: 'text-cyan-600 bg-cyan-100 dark:bg-cyan-900/20',
      strengths: 'text-green-600 bg-green-100 dark:bg-green-900/20',
      improvements: 'text-orange-600 bg-orange-100 dark:bg-orange-900/20',
    };
    return colors[section] || 'text-gray-600 bg-gray-100 dark:bg-gray-900/20';
  };

  // Check if we have any feedback data to show in Executive Summary
  const hasExecutiveSummaryData =
    careerPractice?.feedback?.strengths ||
    careerPractice?.feedback?.areas_for_improvement ||
    careerPractice?.feedback?.candidate_legitimacy?.flag_level ||
    careerPractice?.feedback?.overall_recommendation ||
    careerPractice?.feedback?.job_fit?.Overall_score ||
    careerPractice?.feedback?.coreValue?.Overall_fit ||
    reason;

  const sections = [
    // Executive Summary Section - Consolidates all key feedback
    {
      key: 'executive-summary',
      title: 'Executive Summary',
      condition: hasExecutiveSummaryData,
      content: (
        <div className="space-y-4">
          <div className="mb-4 flex items-center gap-2">
            <Brain className="h-5 w-5 text-purple-600" />
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Key insights and recommendations from the interview assessment
            </p>
          </div>
          <ExecutiveSummaryGrid careerPractice={careerPractice} flag={flag} reason={reason} />
        </div>
      ),
    },

    // Keep Resume Analysis as separate section if needed
    {
      key: 'resume',
      title: 'Resume Analysis',
      condition: careerPractice?.feedback?.resume,
      content: (
        <ResumeSection
          feedback={careerPractice?.feedback?.resume}
          resumeUrl={userProfile?.resumeUrl}
        />
      ),
    },

    // Keep detailed sections for advanced analysis if needed
    {
      key: 'detailed-job-fit',
      title: 'Detailed Job Fit Analysis',
      condition:
        careerPractice?.feedback?.job_fit &&
        Object.keys(careerPractice.feedback.job_fit).length > 1,
      content: (
        <DetailScoreSection
          data={careerPractice?.feedback?.job_fit}
          title="Job Fit Analysis"
          user={userId}
        />
      ),
    },

    {
      key: 'detailed-core-values',
      title: 'Detailed Core Values Assessment',
      condition:
        careerPractice?.feedback?.coreValue &&
        Object.keys(careerPractice.feedback.coreValue).length > 1,
      content: (
        <DetailScoreSection
          data={careerPractice?.feedback?.coreValue}
          title="Core Values Assessment"
          user={userId}
        />
      ),
    },

    {
      key: 'comments',
      title: 'Interview Comments',
      condition: careerPractice?.comments?.[0]?.message,
      content: (
        <Card>
          <CardHeader className="p-3">
            <CardTitle className="text-lg capitalize">Comments</CardTitle>
          </CardHeader>
          <Separator />
          <CardContent className="p-3">
            <p className="text-gray-700 dark:text-gray-300">
              {careerPractice?.comments?.[0]?.message}
            </p>
          </CardContent>
        </Card>
      ),
    },
  ];

  const validSections = sections.filter((section) => section.condition);

  if (validSections.length === 0) {
    return (
      <Card className="bg-gray-50 dark:bg-gray-900/20">
        <CardContent className="p-8 text-center">
          <AlertTriangle className="mx-auto mb-4 h-12 w-12 text-gray-400" />
          <h3 className="mb-2 text-lg font-medium text-gray-600 dark:text-gray-300">
            No Detailed Analysis Available
          </h3>
          <p className="text-gray-500 dark:text-gray-400">
            Detailed analysis will be available once the interview feedback is generated.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {validSections.map((section) => (
        <div key={section.key} className="space-y-2">
          {/* Section Header */}
          <div className="mb-4 flex items-center gap-3">
            <div className={cn('rounded-lg p-2', getSectionColor(section.key))}>
              {getSectionIcon(section.key)}
            </div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
              {section.title}
            </h2>
          </div>

          {/* Section Content */}
          <div className="pl-0">{section.content}</div>
        </div>
      ))}
    </div>
  );
};
