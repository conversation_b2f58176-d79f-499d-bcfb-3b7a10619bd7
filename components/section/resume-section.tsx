'use client';

import { Icon } from '@/icons';
import { AlertCircle, CheckCircle2, FileText, Info } from 'lucide-react';

import { Badge } from '@camped-ui/badge';
import { Button } from '@camped-ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@camped-ui/card';
import { Separator } from '@camped-ui/separator';
import { Tooltip, TooltipContent, TooltipTrigger } from '@camped-ui/tooltip';

import ProgressCircle from '../ui/progress-circle';

export const ResumeSection = ({ feedback, resumeUrl }) => {
  if (!feedback) return null;
  const statusOrder = {
    found: 1,
    relevant: 2,
    not_found: 3,
  };

  // Convert the keywords object to an array of [key, value] pairs
  const keywordArray = Object.entries(feedback?.skills_required);

  // Sort the array based on the statusOrder
  const sortedKeywords = keywordArray.sort(([, statusA]: any, [, statusB]: any) => {
    return statusOrder[statusA] - statusOrder[statusB];
  });

  // Convert the sorted array back to an object
  const sortedKeywordsObject = Object.fromEntries(sortedKeywords);
  const value = Object.values(feedback?.skills_required ?? {})?.filter(
    (item: any) => ['found', 'relevant']?.includes(item),
  )?.length;
  const totalSkills = Object.keys(feedback?.skills_required ?? {})?.length;

  const getIcons = (status) => {
    if (status === 'found') {
      return { icon: 'CheckCircle2', color: 'text-green-700 dark:text-green-500' };
    } else if (status === 'relevant') {
      return { icon: 'CheckCircle2', color: 'text-orange-400' };
    } else if (status === 'not_found') {
      return { icon: 'CircleAlert', color: 'text-destructive' };
    }
  };
  const colorRanges = [
    { scoreRange: [0, 50], color: 'text-destructive' }, // Red
    { scoreRange: [50, 80], color: 'text-orange-400' }, // Orange
    { scoreRange: [80, 100], color: 'text-green-700 dark:text-green-500' }, // Green
  ];
  const strokeColor =
    colorRanges.find(
      (range) =>
        feedback?.skills_matching_score >= range.scoreRange[0] &&
        feedback?.skills_matching_score <= range.scoreRange[1],
    )?.color || '#000000';

  return (
    <Card className="border-cyan-200 bg-cyan-50/50 dark:border-cyan-800 dark:bg-cyan-900/20">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <FileText className="h-5 w-5 text-cyan-600" />
            <CardTitle className="text-lg text-cyan-800 dark:text-cyan-200">
              Resume Analysis
            </CardTitle>
          </div>
          {resumeUrl && (
            <a target="_blank" href={resumeUrl}>
              <Button size="sm" variant="outline" className="h-8">
                Preview Resume
              </Button>
            </a>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Score Summary */}
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <span className="text-xs font-medium text-cyan-600">Skills Match:</span>
              <Badge variant="secondary" className={strokeColor}>
                {value} of {totalSkills} skills
              </Badge>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-xs font-medium text-cyan-600">Score:</span>
              <Badge variant="secondary">{feedback?.skills_matching_score || 0}/100</Badge>
            </div>
          </div>

          {/* Progress Circle and Comments */}
          <div className="flex flex-col gap-4 md:flex-row md:items-start">
            <div className="flex justify-center md:justify-start">
              <ProgressCircle
                percent={feedback?.skills_matching_score}
                width={100}
                height={100}
                strokeWidth={6}
                textSize="xl"
              />
            </div>
            {feedback?.comments && (
              <div className="flex-1">
                <p className="text-sm leading-relaxed text-cyan-700 dark:text-cyan-300">
                  {feedback.comments}
                </p>
              </div>
            )}
          </div>

          {/* Skills Match Section */}
          <div className="mt-6 space-y-3">
            <div className="flex items-center gap-2">
              <h3 className="text-sm font-semibold text-cyan-800 dark:text-cyan-200">
                Skills & Keywords Match
              </h3>
              <Tooltip>
                <TooltipTrigger>
                  <Info className="h-4 w-4 text-cyan-600 opacity-70 hover:opacity-100" />
                </TooltipTrigger>
                <TooltipContent>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <CheckCircle2 className="h-4 w-4 text-green-600" />
                      <span className="text-sm">Skill found</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircle2 className="h-4 w-4 text-orange-500" />
                      <span className="text-sm">Relevant skill found</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <AlertCircle className="h-4 w-4 text-red-500" />
                      <span className="text-sm">Skill not found</span>
                    </div>
                  </div>
                </TooltipContent>
              </Tooltip>
            </div>

            <div className="grid grid-cols-1 gap-2 sm:grid-cols-2 lg:grid-cols-3">
              {Object.entries(sortedKeywordsObject ?? {}).map(([key, value], index) => {
                const metaData = getIcons(value);
                const bgColor =
                  value === 'found'
                    ? 'bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-800'
                    : value === 'relevant'
                    ? 'bg-orange-50 border-orange-200 dark:bg-orange-900/20 dark:border-orange-800'
                    : 'bg-red-50 border-red-200 dark:bg-red-900/20 dark:border-red-800';

                return (
                  <div
                    key={index}
                    className={`flex items-center gap-2 rounded-lg border p-2 ${bgColor}`}
                  >
                    <Icon name={metaData?.icon} className={metaData?.color} size={16} />
                    <span className="text-sm font-medium capitalize">{key}</span>
                  </div>
                );
              })}
            </div>
          </div>
      </CardContent>
    </Card>
  );
};
