import Link from 'next/link';

import { Award, Target, TrendingUp, Users } from 'lucide-react';

import { Badge } from '@camped-ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@camped-ui/card';
import { cn } from '@camped-ui/lib';

import ProgressCircle from '../ui/progress-circle';

function filterFeedbackWithScore(data: any) {
  const filteredFeedback = {};

  for (const category in data) {
    if (typeof data[category] === 'object' && 'score' in data[category]) {
      filteredFeedback[category] = data[category];
    }
  }

  return filteredFeedback;
}

interface DetailScoreSectionProps {
  data: any; // You should replace 'any' with the actual type of 'data'
  title: string;
  user: any;
  premium?: any;
  sampleData?: any; // You should replace 'any' with the actual type of 'sampleData'
}

// Helper function to get appropriate icon for different analysis types
const getAnalysisIcon = (title: string) => {
  if (title.toLowerCase().includes('job fit')) return Award;
  if (title.toLowerCase().includes('core value')) return Users;
  if (title.toLowerCase().includes('performance')) return TrendingUp;
  return Target;
};

// Helper function to get color scheme based on title
const getColorScheme = (title: string) => {
  if (title.toLowerCase().includes('job fit')) {
    return {
      gradient:
        'from-teal-50 via-emerald-50 to-green-50 dark:from-teal-950/20 dark:via-emerald-950/20 dark:to-green-950/20',
      border: 'border-teal-200 dark:border-teal-800',
      iconColor: 'text-teal-600',
      titleColor: 'text-teal-800 dark:text-teal-200',
      badgeColor: 'bg-teal-100 text-teal-800 dark:bg-teal-900 dark:text-teal-200',
    };
  }
  if (title.toLowerCase().includes('core value')) {
    return {
      gradient:
        'from-indigo-50 via-purple-50 to-blue-50 dark:from-indigo-950/20 dark:via-purple-950/20 dark:to-blue-950/20',
      border: 'border-indigo-200 dark:border-indigo-800',
      iconColor: 'text-indigo-600',
      titleColor: 'text-indigo-800 dark:text-indigo-200',
      badgeColor: 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200',
    };
  }
  // Default color scheme
  return {
    gradient:
      'from-blue-50 via-indigo-50 to-purple-50 dark:from-blue-950/20 dark:via-indigo-950/20 dark:to-purple-950/20',
    border: 'border-blue-200 dark:border-blue-800',
    iconColor: 'text-blue-600',
    titleColor: 'text-blue-800 dark:text-blue-200',
    badgeColor: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
  };
};

export const DetailScoreSection = ({
  data,
  title,
  user,
  premium,
  sampleData,
}: DetailScoreSectionProps) => {
  let filteredFeedbackWithScore = {};
  if (user && !premium && sampleData) {
    filteredFeedbackWithScore = sampleData;
  } else {
    filteredFeedbackWithScore = filterFeedbackWithScore(data);
  }
  if (Object.keys(filteredFeedbackWithScore)?.length === Number(0)) return null;

  const IconComponent = getAnalysisIcon(title);
  const colorScheme = getColorScheme(title);

  return (
    <Card className={cn('relative overflow-hidden', colorScheme.border)}>
      {/* Background Gradient */}
      <div className={cn('absolute inset-0 bg-gradient-to-br', colorScheme.gradient)} />

      <CardHeader className="relative pb-4">
        <div className="flex items-center gap-3">
          <IconComponent className={cn('h-6 w-6', colorScheme.iconColor)} />
          <CardTitle className={cn('text-xl font-semibold', colorScheme.titleColor)}>
            {title}
          </CardTitle>
        </div>
      </CardHeader>

      <CardContent className="relative space-y-6">
        {Object.entries(filteredFeedbackWithScore).map(([key, value]: any) => (
          <div
            key={key}
            className="rounded-lg bg-white/60 p-6 backdrop-blur-sm dark:bg-gray-900/60"
          >
            <div className="flex flex-col gap-4 lg:flex-row lg:items-start">
              {/* Score Circle */}
              {(value?.score || value?.score === 0) && (
                <div className="flex flex-col items-center gap-2">
                  <ProgressCircle
                    percent={value?.score}
                    width={100}
                    height={100}
                    strokeWidth={8}
                    textSize="xl"
                  />
                  <Badge className={cn('text-xs font-medium', colorScheme.badgeColor)}>
                    {value?.score}/100
                  </Badge>
                </div>
              )}

              {/* Content */}
              <div className="flex-1 space-y-3">
                <h3 className="text-lg font-semibold capitalize text-gray-900 dark:text-gray-100">
                  {key.replace(/_/g, ' ')}
                </h3>
                <CardDescription className="text-sm leading-relaxed text-gray-700 dark:text-gray-300">
                  {value?.feedback}
                </CardDescription>
              </div>
            </div>
          </div>
        ))}

        {/* Premium Upgrade Overlay */}
        {user && !premium && sampleData && (
          <div className="absolute inset-0 flex items-center justify-center bg-white/80 backdrop-blur-sm dark:bg-gray-900/80">
            <Link
              href="/plans"
              className="group flex scale-100 items-center justify-center gap-x-2 rounded-full bg-gradient-to-r from-blue-600 to-purple-600 px-6 py-3 text-sm font-semibold text-white no-underline shadow-lg transition-all duration-75 hover:from-blue-700 hover:to-purple-700 active:scale-95"
            >
              <Award className="h-4 w-4" />
              <span>Upgrade to Premium</span>
            </Link>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
