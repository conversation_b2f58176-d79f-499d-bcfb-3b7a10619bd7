'use client';

import { format } from 'date-fns';
import {
  Alert<PERSON>ircle,
  AlertTriangle,
  Calendar,
  CheckCircle,
  Clock,
  Download,
  Send,
  Shield,
  Sparkles,
  User,
  XCircle,
} from 'lucide-react';

import { Avatar, AvatarFallback, AvatarImage } from '@camped-ui/avatar';
import { Badge } from '@camped-ui/badge';
import { Button } from '@camped-ui/button';
import { Card, CardContent } from '@camped-ui/card';
import { cn } from '@camped-ui/lib';

import ProgressCircle from '../ui/progress-circle';

interface ModernInterviewHeroProps {
  careerPractice: any;
  userProfile?: any;
  onSendFeedback?: (data: { id: string; withLink: boolean }) => void;
  disableFeedback?: boolean;
  isLoading?: boolean;
  isPublic?: boolean;
  loading?: boolean;
  onGenerateFeedback?: (id: string) => void;
  onInviteCandidate?: (data: { id: string; isInvite: boolean }) => void;
}

export const ModernInterviewHero = ({
  careerPractice,
  userProfile,
  onSendFeedback,
  disableFeedback,
  isLoading,
  isPublic = false,
  loading = false,
  onGenerateFeedback,
  onInviteCandidate,
}: ModernInterviewHeroProps) => {
  // Fix scoring system - it's out of 100, not 10
  const overallScore = careerPractice?.feedback?.overall_score || careerPractice?.finalScore || 0;

  // Fix candidate name and email extraction
  const candidateName =
    userProfile?.userProfile?.fullName ||
    userProfile?.name ||
    careerPractice?.user?.userProfile?.fullName ||
    careerPractice?.user?.name ||
    'Candidate';
  const candidateEmail = userProfile?.email || careerPractice?.user?.email || '';

  const completedTime = careerPractice?.timing?.completedTime;
  const startTime = careerPractice?.timing?.startTime;
  const recommendation = careerPractice?.feedback?.overall_recommendation;
  const riskLevel = careerPractice?.feedback?.candidate_legitimacy?.flag_level;

  // Calculate duration
  const getDuration = () => {
    if (startTime && completedTime) {
      const start = new Date(startTime);
      const end = new Date(completedTime);
      const diffInMinutes = Math.floor((end.getTime() - start.getTime()) / (1000 * 60));
      return `${diffInMinutes} min`;
    }
    return 'N/A';
  };

  // Get recommendation badge
  const getRecommendationBadge = () => {
    if (!recommendation) return null;

    // Handle both string and object formats
    let recommendationStr = '';
    if (typeof recommendation === 'string') {
      recommendationStr = recommendation;
    } else if (typeof recommendation === 'object' && recommendation !== null) {
      // The overall_recommendation is an object with decision and reason properties
      recommendationStr =
        recommendation.decision || recommendation.recommendation || recommendation.status || '';
    } else {
      recommendationStr = String(recommendation);
    }

    if (!recommendationStr) return null;

    // Categorize recommendations properly
    const lowerRecommendation = recommendationStr.toLowerCase();
    const isPositive = ['hire', 'strong hire'].includes(lowerRecommendation);
    const isNeutral = ['weak hire'].includes(lowerRecommendation);
    const isNegative = ['no hire', 'reject', 'not hire'].includes(lowerRecommendation);

    // Determine badge styling based on recommendation type
    let badgeVariant: 'default' | 'destructive' | 'secondary' = 'secondary';
    let badgeClasses = '';
    let icon: React.ReactNode = null;

    if (isPositive) {
      badgeVariant = 'default';
      badgeClasses = 'border-green-200 bg-green-100 text-green-800 hover:bg-green-200';
      icon = <CheckCircle className="h-4 w-4" />;
    } else if (isNeutral) {
      badgeVariant = 'secondary';
      badgeClasses = 'border-yellow-200 bg-yellow-100 text-yellow-800 hover:bg-yellow-200';
      icon = <AlertCircle className="h-4 w-4" />;
    } else {
      badgeVariant = 'destructive';
      badgeClasses = 'border-red-200 bg-red-100 text-red-800 hover:bg-red-200';
      icon = <XCircle className="h-4 w-4" />;
    }

    return (
      <Badge
        variant={badgeVariant}
        className={cn('flex items-center gap-2 px-4 py-2 text-sm font-semibold', badgeClasses)}
      >
        {icon}
        {recommendationStr.toUpperCase()}
      </Badge>
    );
  };

  // Get risk level badge
  const getRiskBadge = () => {
    if (!riskLevel) return null;

    const getRiskColor = (level: string) => {
      switch (level.toLowerCase()) {
        case 'minimal':
        case 'low':
          return 'bg-green-100 text-green-800 border-green-200';
        case 'medium':
          return 'bg-yellow-100 text-yellow-800 border-yellow-200';
        case 'high':
          return 'bg-red-100 text-red-800 border-red-200';
        default:
          return 'bg-gray-100 text-gray-800 border-gray-200';
      }
    };

    const getRiskIcon = (level: string) => {
      switch (level.toLowerCase()) {
        case 'minimal':
        case 'low':
          return <Shield className="h-4 w-4" />;
        case 'medium':
          return <AlertTriangle className="h-4 w-4" />;
        case 'high':
          return <XCircle className="h-4 w-4" />;
        default:
          return <Shield className="h-4 w-4" />;
      }
    };

    return (
      <Badge
        variant="outline"
        className={cn(
          'flex items-center gap-2 px-3 py-1 text-sm font-medium',
          getRiskColor(riskLevel),
        )}
      >
        {getRiskIcon(riskLevel)}
        {riskLevel.toUpperCase()} RISK
      </Badge>
    );
  };

  return (
    <Card className="relative overflow-hidden">
      {/* Background gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-blue-950/20 dark:via-indigo-950/20 dark:to-purple-950/20" />

      <CardContent className="relative p-8">
        <div className="flex flex-col items-start gap-8 lg:flex-row lg:items-center">
          {/* Left side - Candidate info and score */}
          <div className="flex flex-1 items-center gap-6">
            {/* Avatar */}
            <Avatar className="h-20 w-20 border-4 border-white shadow-lg">
              <AvatarImage src={userProfile?.profilePicture} alt={candidateName} />
              <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 text-2xl font-bold text-white">
                {candidateName
                  .split(' ')
                  .map((n: string) => n[0])
                  .join('')
                  .toUpperCase()}
              </AvatarFallback>
            </Avatar>

            {/* Candidate details */}
            <div className="flex-1">
              <h1 className="mb-2 text-3xl font-bold text-gray-900 dark:text-white">
                {candidateName}
              </h1>
              {candidateEmail && (
                <p className="mb-3 flex items-center gap-2 text-gray-600 dark:text-gray-300">
                  <User className="h-4 w-4" />
                  {candidateEmail}
                </p>
              )}

              {/* Role and Level */}
              <div className="mb-4 flex flex-wrap gap-3">
                <Badge variant="secondary" className="px-3 py-1 text-sm">
                  {careerPractice?.role || 'Software Engineer'}
                </Badge>
                <Badge variant="outline" className="px-3 py-1 text-sm">
                  {careerPractice?.level || 'Mid-Level'}
                </Badge>
              </div>

              {/* Recommendation and Risk */}
              <div className="flex flex-wrap gap-3">
                {getRecommendationBadge()}
                {getRiskBadge()}
              </div>
            </div>
          </div>

          {/* Right side - Score and metadata */}
          <div className="flex flex-col items-center gap-8 lg:flex-row">
            {/* Overall Score */}
            <div className="text-center">
              <div className="mb-2">
                <ProgressCircle
                  percent={overallScore}
                  width={120}
                  height={120}
                  strokeWidth={8}
                  textSize="2xl"
                />
              </div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-300">Overall Score</p>
            </div>

            {/* Metadata */}
            <div className="space-y-3 text-sm">
              {completedTime && (
                <div className="flex items-center gap-2 text-gray-600 dark:text-gray-300">
                  <Calendar className="h-4 w-4" />
                  <span>Completed: {format(new Date(completedTime), 'MMM dd, yyyy')}</span>
                </div>
              )}

              <div className="flex items-center gap-2 text-gray-600 dark:text-gray-300">
                <Clock className="h-4 w-4" />
                <span>Duration: {getDuration()}</span>
              </div>

              {careerPractice?.event && (
                <div className="flex items-center gap-2 text-gray-600 dark:text-gray-300">
                  <span className="h-4 w-4 text-center">📋</span>
                  <span>Event: {careerPractice.event}</span>
                </div>
              )}

              {careerPractice?.isPlacement && (
                <div className="flex items-center gap-2 text-gray-600 dark:text-gray-300">
                  <span className="h-4 w-4 text-center">🏆</span>
                  <span>Type: Interviewathon</span>
                </div>
              )}

              {careerPractice?.timing?.inviteTime && (
                <div className="flex items-center gap-2 text-gray-600 dark:text-gray-300">
                  <span className="h-4 w-4 text-center">📧</span>
                  <span>
                    Invited: {format(new Date(careerPractice.timing.inviteTime), 'MMM dd, yyyy')}
                  </span>
                </div>
              )}

              {/* Action buttons */}
              <div className="flex flex-col gap-3">
                {userProfile?.resumeUrl && (
                  <button
                    className="flex items-center gap-2 text-blue-600 transition-colors hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
                    onClick={() => {
                      // Handle resume download
                      window.open(userProfile.resumeUrl, '_blank');
                    }}
                  >
                    <Download className="h-4 w-4" />
                    <span>Download Resume</span>
                  </button>
                )}

                {/* Action Buttons Row */}
                {!isPublic && (
                  <div className="flex flex-col gap-2">
                    {/* Generate AI Feedback Button */}
                    {!careerPractice?.timing?.feedBackGenerateTime && onGenerateFeedback && (
                      <Button
                        className="bg-gradient-to-r from-blue-600 to-purple-600 text-sm font-medium hover:from-blue-700 hover:to-purple-700"
                        onClick={() => onGenerateFeedback(careerPractice?.id)}
                        disabled={loading}
                      >
                        <Sparkles className="mr-2 h-4 w-4" />
                        {loading ? 'Generating...' : 'Generate AI Feedback'}
                      </Button>
                    )}

                    {/* Invite Candidate Button */}
                    {!careerPractice?.timing?.inviteTime &&
                      !careerPractice?.isPlacement &&
                      onInviteCandidate && (
                        <Button
                          variant="outline"
                          onClick={() =>
                            onInviteCandidate({ id: careerPractice?.id, isInvite: true })
                          }
                        >
                          <Send className="mr-2 h-4 w-4" />
                          Invite Candidate
                        </Button>
                      )}

                    {/* Send Feedback Button - Primary Action */}
                    {onSendFeedback && (
                      <Button
                        className="bg-red-600 hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700"
                        onClick={() => onSendFeedback({ id: careerPractice?.id, withLink: false })}
                        disabled={disableFeedback || isLoading}
                      >
                        <XCircle className="mr-2 h-4 w-4" />
                        {careerPractice?.comments?.[0]?.status ?? 'Send Feedback'}
                      </Button>
                    )}
                  </div>
                )}

                {/* Send Feedback Button for Public View */}
                {isPublic && onSendFeedback && (
                  <Button
                    className="bg-red-600 hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700"
                    onClick={() => onSendFeedback({ id: careerPractice?.id, withLink: false })}
                    disabled={disableFeedback || isLoading}
                  >
                    <XCircle className="mr-2 h-4 w-4" />
                    {careerPractice?.comments?.[0]?.status ?? 'Send Feedback'}
                  </Button>
                )}
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
