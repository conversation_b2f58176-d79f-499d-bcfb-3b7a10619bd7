import { useEffect, useRef, useState } from 'react';

import Link from 'next/link';

import { Icon } from '@/icons';
import { AppLogo } from '@/layout/logo';
import { getCalenderByMember, rewriteUserFeedback } from '@/services/apicall';
import { getCookie } from '@/utils/cookies';
import { CalendarIcon } from '@radix-ui/react-icons';
import { format } from 'date-fns';

import { Button } from '@camped-ui/button';
import { Calendar } from '@camped-ui/calendar';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@camped-ui/command';
import { Dialog, DialogContent } from '@camped-ui/dialog';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@camped-ui/form';
import { Label } from '@camped-ui/label';
import { cn } from '@camped-ui/lib';
import { Popover, PopoverContent, PopoverTrigger } from '@camped-ui/popover';
import { RadioGroup, RadioGroupItem } from '@camped-ui/radio-group';
import { TagInput } from '@camped-ui/tag-input';

import { TimePickerDemo } from '../ui/date-time-picker-2/time-picker';

export const RejectModal = ({
  handleSubmit,
  setShowPopup,
  showPopup,
  members,
  handleSchedule,
  form,
  platform,
}) => {
  const [openPopover, setOpenPopover] = useState(false);
  const [openDurationPopover, setOpenDurationPopover] = useState(false);
  const [openVideoCallPopover, setOpenVideoCallPopover] = useState(false);
  const buttonRef = useRef<HTMLButtonElement>(null);
  const [aiLoading, setAiLoading] = useState(false);
  const [loading, setLoading] = useState(false);
  const [buttonWidth, setButtonWidth] = useState(0);
  const [memberCalender, setMemberCalender]: any = useState([]);

  const tenantId = getCookie('aceprepTenantId');

  useEffect(() => {
    if (buttonRef.current) {
      const width = buttonRef.current.getBoundingClientRect().width;
      setButtonWidth(width);
    }
  }, [buttonRef?.current]);

  const handleGenerateReview = async () => {
    setAiLoading(true);
    const aiResponse = await rewriteUserFeedback({
      comment: form.getValues('feedback'),
      status: form.getValues('status'),
    });
    form.setValue('feedback', aiResponse?.feedback?.message);
    setAiLoading(false);
  };
  const handleGetMemberCalender = async () => {
    if (
      !platform ||
      form.watch('meetingType') === 'inPerson' ||
      !form.watch('interviewer')?.[0]?.user?.email ||
      !form.watch('selectedDate')
    )
      return null;
    setLoading(true);
    const response = await getCalenderByMember({
      organizationId: tenantId,
      date: form.watch('selectedDate'),
      user: form
        .watch('interviewer')
        .map((interviewer) => interviewer.user.email)
        ?.join(','),
      duration: form.watch('meetingDuration') ?? 60,
      platform,
    });
    setMemberCalender(response?.calendar);
    setLoading(false);
  };

  return (
    <Dialog open={showPopup} onOpenChange={() => setShowPopup(false)}>
      <DialogContent className="max-h-[90vh] max-w-2xl overflow-hidden p-0">
        {/* Header */}
        <div className="relative border-b bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-4 dark:from-blue-950/20 dark:to-indigo-950/20">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Link className="text-xl font-semibold text-gray-800" href="/">
                <AppLogo />
              </Link>
              <div className="h-6 w-px bg-gray-300 dark:bg-gray-600" />
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Feedback</h2>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto px-6 py-6">
          <div className="space-y-6">
            <Form {...(form as any)} className="space-y-6">
              {/* Status Field */}
              <FormField
                control={form.control as any}
                name="status"
                render={({ field }) => {
                  return (
                    <FormItem className="space-y-3">
                      <FormLabel className="text-sm font-medium text-gray-900 dark:text-white">
                        Status
                      </FormLabel>
                      <FormControl>
                        <Popover open={openPopover}>
                          <PopoverContent style={{ width: buttonWidth }} className="p-2">
                            <div className="w-full space-y-1">
                              <button
                                className="flex w-full items-center justify-start gap-3 rounded-lg bg-background p-3 text-left text-sm transition-all duration-200 hover:bg-red-50 hover:text-red-700 dark:hover:bg-red-950/20"
                                onClick={() => {
                                  field.onChange('Rejected');
                                  setOpenPopover(false);
                                }}
                              >
                                <div className="h-2 w-2 rounded-full bg-red-500"></div>
                                <span className="font-medium">Rejected</span>
                              </button>
                              <button
                                className="flex w-full items-center justify-start gap-3 rounded-lg bg-background p-3 text-left text-sm transition-all duration-200 hover:bg-green-50 hover:text-green-700 dark:hover:bg-green-950/20"
                                onClick={() => {
                                  field.onChange('Accepted');
                                  setOpenPopover(false);
                                }}
                              >
                                <div className="h-2 w-2 rounded-full bg-green-500"></div>
                                <span className="font-medium">Accepted</span>
                              </button>
                              <button
                                className="flex w-full items-center justify-start gap-3 rounded-lg bg-background p-3 text-left text-sm transition-all duration-200 hover:bg-yellow-50 hover:text-yellow-700 dark:hover:bg-yellow-950/20"
                                onClick={() => {
                                  field.onChange('On Hold');
                                  setOpenPopover(false);
                                }}
                              >
                                <div className="h-2 w-2 rounded-full bg-yellow-500"></div>
                                <span className="font-medium">On Hold</span>
                              </button>
                              <button
                                className="flex w-full items-center justify-start gap-3 rounded-lg bg-background p-3 text-left text-sm transition-all duration-200 hover:bg-blue-50 hover:text-blue-700 dark:hover:bg-blue-950/20"
                                onClick={() => {
                                  field.onChange('Move to next round');
                                  setOpenPopover(false);
                                }}
                              >
                                <div className="h-2 w-2 rounded-full bg-blue-500"></div>
                                <span className="font-medium">Move to next round</span>
                              </button>
                            </div>
                          </PopoverContent>
                          <PopoverTrigger asChild>
                            <button
                              onClick={() => setOpenPopover(!openPopover)}
                              className="flex w-full items-center justify-between rounded-lg border border-gray-200 bg-white px-4 py-3 text-gray-900 shadow-sm transition-all duration-200 hover:border-gray-300 hover:shadow-md focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500/20 dark:border-gray-700 dark:bg-gray-800 dark:text-white dark:hover:border-gray-600"
                              ref={buttonRef}
                            >
                              <div className="flex items-center gap-3">
                                {field.value === 'Rejected' && (
                                  <div className="h-2 w-2 rounded-full bg-red-500"></div>
                                )}
                                {field.value === 'Accepted' && (
                                  <div className="h-2 w-2 rounded-full bg-green-500"></div>
                                )}
                                {field.value === 'On Hold' && (
                                  <div className="h-2 w-2 rounded-full bg-yellow-500"></div>
                                )}
                                {field.value === 'Move to next round' && (
                                  <div className="h-2 w-2 rounded-full bg-blue-500"></div>
                                )}
                                <span className="font-medium">
                                  {field?.value ?? 'Select Status'}
                                </span>
                              </div>
                              <Icon name="ChevronsUpDown" className="h-4 w-4 text-gray-400" />
                            </button>
                          </PopoverTrigger>
                        </Popover>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  );
                }}
              />
              {/* Comments Field */}
              {['Rejected', 'Accepted', 'On Hold'].includes(form.watch('status')) && (
                <FormField
                  control={form.control as any}
                  name="feedback"
                  render={({ field }) => {
                    return (
                      <FormItem className="space-y-3">
                        <div className="flex items-center justify-between">
                          <FormLabel className="text-sm font-medium text-gray-900 dark:text-white">
                            Comments
                          </FormLabel>
                          <button
                            type="button"
                            onClick={
                              aiLoading
                                ? () => {}
                                : () => {
                                    handleGenerateReview();
                                  }
                            }
                            disabled={aiLoading}
                            className="flex items-center gap-2 rounded-md bg-gradient-to-r from-blue-600 to-indigo-600 px-3 py-1.5 text-xs font-medium text-white shadow-sm transition-all duration-200 hover:from-blue-700 hover:to-indigo-700 hover:shadow-md disabled:cursor-not-allowed disabled:opacity-50"
                          >
                            {aiLoading ? (
                              <Icon name="Loader2" className="h-3 w-3 animate-spin" />
                            ) : (
                              <Icon name="Sparkles" className="h-3 w-3" />
                            )}
                            Rewrite with AI
                          </button>
                        </div>
                        <FormControl>
                          <textarea
                            placeholder="Share your detailed feedback about the candidate's performance..."
                            {...field}
                            rows={5}
                            className="w-full rounded-lg border border-gray-200 bg-white px-4 py-3 text-sm text-gray-900 shadow-sm transition-all duration-200 placeholder:text-gray-500 hover:border-gray-300 focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500/20 dark:border-gray-700 dark:bg-gray-800 dark:text-white dark:placeholder:text-gray-400 dark:hover:border-gray-600 dark:focus:border-blue-400"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    );
                  }}
                />
              )}
              {form.watch('status') === 'Move to next round' && (
                <FormField
                  control={form.control as any}
                  name="meetingType"
                  render={({ field }) => {
                    return (
                      <FormItem className="col-span-1 flex w-full flex-col items-start">
                        <FormLabel htmlFor="name" className="flex-[1] text-base">
                          Meeting Type
                        </FormLabel>
                        <FormControl className="">
                          <RadioGroup>
                            <div className="flex items-center space-x-2">
                              <RadioGroupItem
                                value={`inPerson`}
                                id={`inPerson`}
                                onClick={() => form.setValue('meetingType', 'inPerson')}
                                checked={field.value === 'inPerson'}
                              />
                              <Label htmlFor={`inPerson`} className="px-2 text-base font-medium">
                                In Person
                              </Label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <RadioGroupItem
                                value={`videoCall`}
                                id={`videoCall`}
                                onClick={() => form.setValue('meetingType', 'videoCall')}
                                checked={field.value === 'videoCall'}
                              />
                              <Label htmlFor={`videoCall`} className="px-2 text-base font-medium">
                                Video Call
                              </Label>
                            </div>
                          </RadioGroup>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    );
                  }}
                />
              )}
              {form.watch('status') === 'Move to next round' && (
                <FormField
                  control={form.control as any}
                  name="interviewer"
                  render={({ field }) => {
                    return (
                      <FormItem className="col-span-1 flex w-full flex-col items-start">
                        <FormLabel htmlFor="name" className="flex-[1] text-base">
                          Interviewer
                        </FormLabel>
                        <FormControl className="text-left">
                          <TagInput
                            placeholder="Select interviewers"
                            tags={
                              Array.isArray(field?.value)
                                ? field?.value
                                : field?.value
                                ? [field?.value]
                                : []
                            }
                            enableAutocomplete
                            restrictTagsToAutocompleteOptions
                            autocompleteOptions={members?.items?.map((item) => {
                              return {
                                ...item,
                                text: item?.user?.email,
                              };
                            })}
                            className="text-left sm:min-w-[450px]"
                            setTags={(newTags: any) => {
                              field.onChange(newTags);
                              handleGetMemberCalender();
                              setOpenVideoCallPopover(false);
                            }}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    );
                  }}
                />
              )}
              {form.watch('status') === 'Move to next round' &&
                platform &&
                form.watch('meetingType') !== 'inPerson' && (
                  <FormField
                    control={form.control as any}
                    name="meetingDuration"
                    render={({ field }) => {
                      const { value, onChange } = field;
                      return (
                        <FormItem className="col-span-1 flex w-full flex-col items-start">
                          <FormLabel htmlFor="name" className="flex-[1] text-base">
                            Interview Duration
                          </FormLabel>
                          <FormControl className="">
                            <Popover open={openDurationPopover}>
                              <PopoverContent style={{ width: buttonWidth }} className="p-1">
                                <div className="w-full rounded-md bg-background">
                                  <button
                                    className="flex w-full items-center justify-start space-x-2 rounded-md bg-background p-2 text-left text-sm transition-all duration-75 hover:bg-secondary"
                                    onClick={() => {
                                      field.onChange('30');
                                      setOpenDurationPopover(false);
                                    }}
                                  >
                                    30 Mins
                                  </button>
                                  <button
                                    className="flex w-full items-center justify-start space-x-2 rounded-md bg-background p-2 text-left text-sm transition-all duration-75 hover:bg-secondary"
                                    onClick={() => {
                                      field.onChange('60');
                                      setOpenDurationPopover(false);
                                    }}
                                  >
                                    60 Mins
                                  </button>
                                </div>
                              </PopoverContent>
                              <PopoverTrigger asChild>
                                <button
                                  onClick={() => setOpenDurationPopover(!openDurationPopover)}
                                  className="flex w-full items-center justify-between rounded-md border border-gray-300 bg-gray-50 px-4 py-2 text-gray-900 transition-all duration-75 hover:border-gray-800 focus:outline-none active:bg-gray-100 sm:text-sm"
                                  ref={buttonRef}
                                >
                                  <p className="text-gray-600">
                                    {field?.value === '30' ? '30 Mins' : '60 Mins'}
                                  </p>
                                </button>
                              </PopoverTrigger>
                            </Popover>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      );
                    }}
                  />
                )}
              {form.watch('status') === 'Move to next round' && (
                <FormField
                  control={form.control as any}
                  name={
                    platform && form.watch('meetingType') !== 'inPerson'
                      ? 'selectedDate'
                      : 'meetingDateTime'
                  }
                  render={({ field }) => {
                    const { value, onChange } = field;
                    return (
                      <FormItem className="col-span-1 flex w-full flex-col items-start">
                        <FormLabel htmlFor="name" className="flex-[1] text-base">
                          Meeting Time
                        </FormLabel>
                        <FormControl className="">
                          <Popover>
                            <PopoverTrigger asChild>
                              <Button
                                variant={'outline'}
                                className={cn(
                                  'w-full pl-3 text-left font-normal',
                                  !value && 'text-muted-foreground',
                                )}
                              >
                                {value ? (
                                  format(
                                    field.value,
                                    platform && form.watch('meetingType') !== 'inPerson'
                                      ? 'PPP'
                                      : 'PPP  HH:mm',
                                  )
                                ) : (
                                  <span>Pick a date</span>
                                )}
                                <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                              </Button>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0" align="start">
                              <Calendar
                                mode="single"
                                selected={field.value}
                                onSelect={(date) => {
                                  field.onChange(date);
                                  handleGetMemberCalender();
                                }}
                                initialFocus
                              />
                              {(!platform || form.watch('meetingType') === 'inPerson') && (
                                <div className="border-t border-border p-3">
                                  <TimePickerDemo
                                    setDate={field.onChange}
                                    date={new Date(field.value)}
                                  />
                                </div>
                              )}
                            </PopoverContent>
                          </Popover>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    );
                  }}
                />
              )}

              {form.watch('status') === 'Move to next round' &&
                form.watch('meetingType') !== 'inPerson' &&
                platform && (
                  <FormField
                    control={form.control as any}
                    name="meetingDateTime"
                    render={({ field }) => {
                      const { value, onChange } = field;
                      return (
                        <FormItem className="col-span-1 flex w-full flex-col items-start">
                          <FormControl className="">
                            <>
                              {loading && (
                                <div className="flex items-center gap-2">
                                  <Icon name="Loader2" className="h-4 w-4 animate-spin" />
                                  <p>Fetching meeting timing</p>
                                </div>
                              )}
                              {!loading && form.watch('selectedDate') && !memberCalender && (
                                <p>No available timing on selected date</p>
                              )}
                              <div className="flex flex-wrap gap-2">
                                {memberCalender?.map((item, index) => (
                                  <Button
                                    size="sm"
                                    variant={item?.start === value ? 'default' : 'secondary'}
                                    className="mb-2 mr-2"
                                    key={index}
                                    onClick={() => {
                                      field.onChange(item?.start);
                                      form.setValue('endTime', item?.end);
                                    }}
                                  >
                                    {format(item?.start, 'HH:mm')}-{format(item?.end, 'HH:mm')}
                                  </Button>
                                ))}
                              </div>
                            </>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      );
                    }}
                  />
                )}
              {form.watch('status') === 'Move to next round' &&
                form.watch('meetingType') === 'inPerson' && (
                  <FormField
                    control={form.control as any}
                    name="address"
                    render={({ field }) => {
                      return (
                        <FormItem className="col-span-1 flex w-full flex-col items-start">
                          <FormLabel htmlFor="name" className="flex-[1] text-base">
                            Address
                          </FormLabel>
                          <FormControl className="">
                            <textarea
                              placeholder="Enter your Address"
                              {...field}
                              rows={2}
                              className="h-28 w-full rounded border bg-inherit p-2 text-sm focus:outline-2 focus:outline-offset-4 focus:outline-primary"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      );
                    }}
                  />
                )}
              {/* Action Buttons */}
              <div className="mt-8 border-t pt-6">
                {['Rejected'].includes(form.watch('status')) && (
                  <div className="flex flex-col gap-3 sm:flex-row">
                    <Button
                      type="button"
                      onClick={() => {
                        handleSubmit(false, true);
                      }}
                      className="flex-1 bg-gradient-to-r from-blue-600 to-indigo-600 text-white shadow-lg transition-all duration-200 hover:from-blue-700 hover:to-indigo-700 hover:shadow-xl"
                    >
                      <Icon name="Send" className="mr-2 h-4 w-4" />
                      Share Feedback
                    </Button>
                    <Button
                      type="button"
                      onClick={() => {
                        handleSubmit(true, true);
                      }}
                      variant="outline"
                      className="flex-1 border-blue-200 text-blue-700 transition-all duration-200 hover:border-blue-300 hover:bg-blue-50"
                    >
                      <Icon name="Link" className="mr-2 h-4 w-4" />
                      Share with Link
                    </Button>
                    <Button
                      type="button"
                      onClick={() => {
                        handleSubmit(false, false);
                      }}
                      variant="secondary"
                      className="bg-gray-100 text-gray-700 transition-all duration-200 hover:bg-gray-200"
                    >
                      <Icon name="Save" className="mr-2 h-4 w-4" />
                      Save
                    </Button>
                  </div>
                )}
                {['On Hold', 'Accepted'].includes(form.watch('status')) && (
                  <div className="flex justify-end">
                    <Button
                      type="button"
                      onClick={() => {
                        handleSubmit(false, false);
                      }}
                      className="bg-gradient-to-r from-green-600 to-emerald-600 text-white shadow-lg transition-all duration-200 hover:from-green-700 hover:to-emerald-700 hover:shadow-xl"
                    >
                      <Icon name="Save" className="mr-2 h-4 w-4" />
                      Save Feedback
                    </Button>
                  </div>
                )}
                {form.watch('status') === 'Move to next round' && (
                  <div className="flex justify-end">
                    <Button
                      type="button"
                      onClick={() => {
                        handleSchedule(false);
                      }}
                      className="bg-gradient-to-r from-purple-600 to-pink-600 text-white shadow-lg transition-all duration-200 hover:from-purple-700 hover:to-pink-700 hover:shadow-xl"
                    >
                      <Icon name="Calendar" className="mr-2 h-4 w-4" />
                      Schedule Interview
                    </Button>
                  </div>
                )}
              </div>
            </Form>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
