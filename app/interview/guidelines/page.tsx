import { redirect } from 'next/navigation';

import { InterviewGuidelinesScreen } from '@/components/interview-guidelines/interview-guidelines-screen';
import { authOptions } from '@/lib/auth';
import { getInterviewDetails } from '@/services/apicall';
import { getServerSession } from 'next-auth';

export default async function Onboarding(props) {
  const id = props.searchParams.id;
  const session = await getServerSession(authOptions);

  const interviewDetails = await getInterviewDetails(id);

  if (!interviewDetails) {
    return redirect('/404');
  }

  const timing = interviewDetails?.result?.timing;

  if (interviewDetails?.result?.hasCompleted || timing?.completedTime) {
    return redirect(`/completed?id=${id}`);
  }

  return (
    <InterviewGuidelinesScreen session={session} interviewDetails={interviewDetails?.result} />
  );
}
