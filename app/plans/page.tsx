import { redirect } from 'next/navigation';

import { PlansScreen } from '@/components/wrapper-screen/plans-screen';
import { authOptions } from '@/lib/auth';
import { getServerSession } from 'next-auth';

export default async function MyPreferences() {
  const session: any = await getServerSession(authOptions);

  if (process.env.NEXT_PUBLIC_PLATFORM === 'hire' || session?.memberships?.length > 0 || !session) {
    redirect('/404');
  }
  const fetchPlans = async () => {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/lookups/pricingPlans`,
    );
    if (response.ok) {
      const data = await response.json();
      return data?.items;
    } else {
      throw new Error('Failed to fetch plans.');
    }
  };

  const fetchPlanData = await fetchPlans();
  return <PlansScreen session={session} pricePlan={fetchPlanData} />;
}
