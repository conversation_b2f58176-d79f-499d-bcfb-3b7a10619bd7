import { NextResponse } from 'next/server';

import StaffInterviewNotification from '@/emails/staff-video-meeting';
import AcceptCandidate from '@/emails/video-call';
import campedMailer from '@/lib/campedMailer';
import { db } from '@/prisma/db';
import { addParticipant, createMeeting } from '@/services/apicall';
import { render } from '@react-email/render';

export async function POST(request) {
  try {
    const {
      interview_details,
      job_applicant,
      job_opening,
      custom_meeting_type,
      scheduled_on,
      from_time,
      to_time,
      name,
    } = await request.json();
    if (custom_meeting_type !== 'Video Call')
      return NextResponse.json({ error: 'Invalid meeting type' }, { status: 400 });
    const tenantId = request.headers.get('tenant-id');
    const from = from_time
      .split(':')
      .map((part) => part.padStart(2, '0'))
      .join(':');
    const to = to_time
      .split(':')
      .map((part) => part.padStart(2, '0'))
      .join(':');
    let staffs, meeting;
    const meetingDateTime = new Date(`${scheduled_on}T${from}`);
    const endTime = new Date(`${scheduled_on}T${to}`);
    const interviewDuration = (endTime.getTime() - meetingDateTime.getTime()) / (1000 * 60);

    const careerPractice: any = await db.careerPractice.findFirst({
      where: {
        eventDetails: {
          references_id: job_opening,
          status: 'ACTIVE',
          organizationId: tenantId,
        },
        user: {
          email: job_applicant,
        },
      },
    });
    // Retrieve event details from the database
    const eventDetails = await db.eventDetails.findFirst({
      where: {
        references_id: job_opening,
        status: 'ACTIVE',
        organizationId: tenantId,
      },
      include: { organization: true },
    });

    if (!eventDetails) {
      return NextResponse.json({ error: 'Career practice not found' }, { status: 404 });
    }

    if (interview_details?.length > 0 && interview_details) {
      staffs = await db.user.findMany({
        where: {
          email: { in: interview_details?.map((item) => item?.interviewer) },
        },
        include: {
          userProfile: {
            select: { fullName: true },
          },
        },
      });
    }

    const candidate = await db.user.findFirst({
      where: { email: job_applicant },
      include: { userProfile: { select: { fullName: true } } },
    });

    const candidateName =
      candidate?.userProfile?.fullName === '' || !candidate?.userProfile?.fullName
        ? candidate?.email
        : candidate?.userProfile?.fullName;

    const response = await createMeeting(`${eventDetails?.name} - ${candidate?.email}`);
    const meetingId = response?.id?.data?.id;
    if (!meetingId) {
      return NextResponse.json({ error: 'Failed to create meeting' }, { status: 500 });
    }
    const userParticipant = await addParticipant(
      meetingId,
      candidate?.id,
      'group_call_participant',
    );
    const staffParticipants = await Promise.all(
      staffs?.map(async (item) => {
        const staff = await addParticipant(meetingId, item?.id, 'group_call_participant');
        return { ...staff, userId: item?.id };
      }),
    );
    console.log({ staffParticipants });

    const [userMail, staffEmail] = await Promise.all([
      sendEmail({
        email: candidate?.email,
        location: '',
        inPerson: false,
        interviewTime: `${meetingDateTime}`,
        organization: eventDetails?.organization,
        isStaff: false,
        candidateName: '',
        staffName: '',
        interviewRole: eventDetails?.role,
        candidateLink: '',
      }),
      ...staffs?.map(async (item) => {
        const staffName =
          item?.userProfile?.fullName === '' || !item?.userProfile?.fullName
            ? item?.email
            : item?.userProfile?.fullName;

        const staff = await sendEmail({
          email: item?.email,
          location: '',
          inPerson: false,
          interviewTime: `${meetingDateTime}`,
          organization: eventDetails?.organization,
          isStaff: true,
          candidateName: candidateName,
          staffName: staffName,
          interviewRole: eventDetails?.role,
          candidateLink: '',
        });
        return staff;
      }),
    ]);
    if (!userMail) {
      return NextResponse.json({ error: 'Failed to send email' }, { status: 500 });
    }
    meeting = await db.videoCallInterview.create({
      data: {
        careerPracticeId: careerPractice?.id ?? '',
        eventId: eventDetails?.id,
        meetingId,
        referenceId: name,
        participants: [
          {
            userId: candidate?.id,
            email: candidate?.email,
            meetingUserId: userParticipant?.id?.data?.id,
            meetingUserToken: userParticipant?.id?.data?.token,
            userMail,
          },
          ...staffs?.map((item) => {
            const staff = staffParticipants?.find(
              (participant) => participant?.userId === item?.id,
            );
            return {
              userId: item?.id,
              email: item?.email,
              meetingUserId: staff?.id?.data?.id,
              meetingUserToken: staff?.id?.data?.token,
            };
          }),
        ],
        meetingType: 'videoCall',
        scheduleTime: convertISTtoGMT(meetingDateTime),
        interviewerId: staffs?.map((item) => item?.id),
        userId: candidate?.id,
        address: '',
        interviewDuration: `${interviewDuration}`,
        organizationId: eventDetails?.organizationId,
        meetingDetails: {
          calenderEvent: staffEmail?.calenderEvent,
        },
      },
    });

    console.log('Meeting created:', meeting);
    if (!meeting) {
      return NextResponse.json({ error: 'Failed to create meeting' }, { status: 500 });
    }

    return NextResponse.json({ meeting }, { status: 200 });
  } catch (error) {
    console.error('Error in processing request:', error);
    return NextResponse.json(
      { error: error.message || 'Request processing failed' },
      { status: 500 },
    );
  }
}

const generateEmailUrl = (baseUrl: string, userEmail: string, token: string) => {
  return `${baseUrl}/api/auth/callback/${encodeURIComponent('email')}?email=${encodeURIComponent(
    userEmail,
  )}&token=${encodeURIComponent(token)}&callbackUrl=${encodeURIComponent(
    `${baseUrl}/my-interviews`,
  )}`;
};

const sendEmail = async ({
  email,
  location,
  inPerson,
  interviewTime,
  organization,
  isStaff,
  candidateName,
  staffName,
  interviewRole,
  candidateLink,
}) => {
  let emailUrl;
  if (email) {
    emailUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/sign-in?from=/my-interviews`;
  }
  if (isStaff) {
    const html = render(
      StaffInterviewNotification({
        meetingUrl: emailUrl,
        location,
        inPerson,
        staffName,
        candidateName,
        interviewRole,
        organization: organization?.name,
        emailIdentifier: null,
        interviewTime,
        candidateLink,
      }),
      {
        pretty: true,
      },
    );
    const mailResponse = await campedMailer.send({
      from: process.env.SEND_EMAIL_FROM || '',
      to: email,
      subject: `Interview scheduled with ${candidateName}`,
      html,
    });

    return { ...mailResponse };
  }
  const mailResponse = await campedMailer.send({
    from: process.env.SEND_EMAIL_FROM || '',
    to: email,
    subject: `Congratulations! You’re Moving Forward in the Interview Process at ${organization?.name}`,
    html: render(
      AcceptCandidate({
        organization: organization?.name,
        meetingUrl: emailUrl,
        location,
        inPerson,
        emailContent: null,
        emailIdentifier: null,
        interviewTime: interviewTime,
        role: interviewRole,
      }),
      {
        pretty: true,
      },
    ),
  });
  return mailResponse;
};

function convertISTtoGMT(istDate) {
  // Create a new Date object with the IST date
  const istTime = new Date(istDate);

  // Get the UTC time by subtracting the IST offset (5 hours 30 minutes)
  const gmtTime = new Date(istTime.getTime() - (5 * 60 + 30) * 60000);

  return gmtTime;
}
