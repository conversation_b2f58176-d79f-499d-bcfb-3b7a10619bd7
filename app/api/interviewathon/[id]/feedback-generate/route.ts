import { NextResponse } from 'next/server';

import { languageOptions } from '@/constants/languageOptions';
import { OpenAIStreamPayload } from '@/lib/OpenAIStream';
import { GCPVertexAI } from '@/lib/ai-models/src/models/gcp-vertexai';
import { VertexAIConfiguration } from '@/lib/ai-models/src/types/vertexai-model';
import retryOpenAI from '@/lib/retryOpenApi';
import { updateFeedbackBasedOnConversation } from '@/pages/api/server/admin/cron/feedback-result';
import { db } from '@/prisma/db';
import { generatePrompt } from '@/prompt-service';
import { generateConsolidatedFeedback } from '@/services/apicall';
import base64 from 'base-64';
import { v4 as uuid } from 'uuid';

export const maxDuration = 299;

if (!process.env.OPENAI_API_KEY) {
  throw new Error('Missing env var from OpenAI');
}

const project = process.env.NEXT_PUBLIC_GCP_PROJECT_ID || '';
const model = process.env.NEXT_PUBLIC_GCP_MODEL || '';
const location = process.env.NEXT_PUBLIC_GCP_LOCATION || '';
const defaultBucket = process.env.NEXT_PUBLIC_GCP_BUCKET || '';
const folder = process.env.NEXT_PUBLIC_S3FOLDER || '';

// Function to get GCP bucket from organization or use default
async function getGCPBucket(organizationId) {
  if (!organizationId) return defaultBucket;

  try {
    const organization = await db.organization.findUnique({
      where: { id: organizationId },
      select: { gcpBucket: true },
    });

    return organization?.gcpBucket || defaultBucket;
  } catch (error) {
    console.error('Error fetching organization GCP bucket:', error);
    return defaultBucket;
  }
}
const generateCodingSystemMessage = async (careerPractice: any) => {
  const systemPrompt = await generatePrompt(
    'INTERVIEWATHON_CODING_FEEDBACK_SYSTEM_PROMPT_STRATEGY',
    {
      role: careerPractice?.role,
      level: careerPractice?.level,
    },
  );
  return {
    role: 'system',
    content: systemPrompt,
  };
};

const generateFrontendSystemMessage = async (careerPractice: any) => {
  const systemPrompt = await generatePrompt(
    'INTERVIEWATHON_FRONTEND_FEEDBACK_SYSTEM_PROMPT_STRATEGY',
    {
      role: careerPractice?.role,
      level: careerPractice?.level,
    },
  );
  return {
    role: 'system',
    content: systemPrompt,
  };
};

const generateCodingUserMessage = async (item: any, decodedAnswer) => {
  const language = languageOptions?.find((language) => language?.id === item?.languages?.[0]);
  const userPrompt = await generatePrompt('INTERVIEWATHON_CODING_FEEDBACK_USER_PROMPT_STRATEGY', {
    language: language?.value,
    title: item?.question,
    description: item?.questionDescription,
    inputCode: decodedAnswer,
  });
  return {
    role: 'user',
    content: userPrompt,
  };
};

const generateFrontendUserMessage = async (
  item: any,
  dependencies,
  templateName,
  fileContentString,
  boilerPlate,
) => {
  const userPrompt = await generatePrompt('INTERVIEWATHON_FRONTEND_FEEDBACK_USER_PROMPT_STRATEGY', {
    dependencies,
    templateName,
    question: item?.question,
    questionDescription: item?.questionDescription,
    fileContentString,
    boilerPlate,
  });
  return {
    role: 'user',
    content: userPrompt,
  };
};

const generateFeedback = async (messages) => {
  const payload: OpenAIStreamPayload = {
    model: process.env.AZURE_OPEN_AI_MODEL || '',
    messages: messages,
    temperature: 0.7,
    top_p: 0.8,
    frequency_penalty: 0,
    presence_penalty: 0,
    stream: false,
    n: 1,
    response_format: { type: 'json_object' },
  };
  const result = await retryOpenAI(payload, 3, true);
  return result;
};

export async function POST(request: any) {
  const { id, organizationId } = await request.json();

  const careerPractice: any = await db.careerPractice.findUnique({
    where: {
      id: id,
    },
    include: {
      eventDetails: {
        select: {
          isPlacement: true,
          organizationId: true,
          aiQuestionCount: true,
          isAiQuestion: true,
          questions: true,
        },
      },
    },
  });

  if ((careerPractice?.feedback as any)?.overall_score) {
    return NextResponse.json({ message: 'Feedback Generated' }, { status: 200 });
  }

  if (!(careerPractice?.conversation as any)?.length) {
    return NextResponse.json({ error: 'Error' }, { status: 400 });
  }
  let conversation: any = careerPractice?.conversation;

  const feedbackPromises = conversation?.map(async (item: any) => {
    if (item?.round === 'coding-interview') {
      if (!item?.status && !item?.memory && !item?.time && !item?.isAnswered) {
        return {
          short_summary: 'The candidate did not respond to the question.',
          overall_score: 0,
          code_quality: {
            score: 0,
            feedback: '',
          },
          problem_solving: {
            score: 0,
            feedback: '',
          },
          efficiency: {
            score: 0,
            feedback: '',
          },
          complexity: {
            score: 0,
            feedback: '',
          },
          algorithm_choice: {
            score: 0,
            feedback: '',
          },
          readability: {
            score: 0,
            feedback: '',
          },
        };
      }
      const decodedAnswer = base64.decode(item?.source_code);
      const itemMessages = [
        await generateCodingSystemMessage(careerPractice),
        await generateCodingUserMessage(item, decodedAnswer),
      ];
      return await generateFeedback(itemMessages);
    } else if (item?.round === 'frontend-interview') {
      if (!item?.isAnswered) {
        return {
          short_summary: 'The candidate did not respond to the question.',
          overall_score: 0,
          code_quality: {
            score: 0,
            feedback: '',
          },
          problem_solving: {
            score: 0,
            feedback: '',
          },
          efficiency: {
            score: 0,
            feedback: '',
          },
          complexity: {
            score: 0,
            feedback: '',
          },
          frontend_choice: {
            score: 0,
            feedback: '',
          },
          readability: {
            score: 0,
            feedback: '',
          },
        };
      }
      const { files, dependencies, templateName } = item?.source_code;
      const question = careerPractice?.eventDetails?.questions?.find(
        (localQuestion) => localQuestion?.questionId === item?.questionId,
      );
      const boilerPlate = question?.source_code?.files.map((fileItem) => {
        return {
          ...fileItem,
          fileData: base64.decode(fileItem?.fileData),
        };
      });

      const fileContent = files.map((fileItem) => {
        return {
          ...fileItem,
          fileData: base64.decode(fileItem?.fileData),
        };
      });

      const fileContentString = JSON.stringify(fileContent);

      const itemMessages = [
        await generateFrontendSystemMessage(careerPractice),
        await generateFrontendUserMessage(
          item,
          dependencies,
          templateName,
          fileContentString,
          JSON.stringify(boilerPlate),
        ),
      ];

      return await generateFeedback(itemMessages);
    } else if (item?.round === 'multiple-choice') {
      return item?.feedback;
    } else if (
      item?.round === 'video-interview' &&
      careerPractice?.eventDetails?.isAiQuestion &&
      item?.isAnswered
    ) {
      let systemPrompt = await generatePrompt(
        'INTERVIEWATHON_VIDEO_FEEDBACK_SYSTEM_PROMPT_STRATEGY',
        {
          role: careerPractice?.role,
          level: careerPractice?.level,
          evaluation: careerPractice?.eventDetails?.evaluation,
        },
      );
      console.log({ item });
      // Get bucket from organization
      const bucket = await getGCPBucket(
        careerPractice?.eventDetails?.organizationId || organizationId,
      );

      const response = await generateVideoResult({
        systemInput: systemPrompt,
        url: item?.s3Id,
        question: item?.question,
        bucket,
      });
      return JSON.parse(response?.parts?.[0]?.text);
    } else {
      return (
        item?.feedback ?? {
          impact: {
            score: 0,
            feedback: '',
          },
          clarity: {
            score: 0,
            feedback: '',
          },
          passion: {
            score: 0,
            feedback: '',
          },
          strengths: [],
          confidence: {
            score: 0,
            feedback: '',
          },
          transcript: '',
          communication: {
            score: 0,
            feedback: '',
          },
          overall_score: 0,
          short_summary: '',
          areas_of_improvement: [],
          language_proficiency: {
            score: 0,
            feedback: '',
          },
        }
      );
    }
  });

  const individualFeedback = await Promise.all(feedbackPromises);
  const updatedConversation = conversation?.map((item: any, index: number) => {
    return {
      ...item,
      feedback: individualFeedback[index],
    };
  });

  const overAllResult = updateFeedbackBasedOnConversation(updatedConversation);
  if (!careerPractice?.eventId || careerPractice?.eventDetails?.isPlacement) {
    await db.leaderboard.create({
      data: {
        organizationId: careerPractice?.eventDetails?.organizationId ?? organizationId,
        userId: careerPractice?.userId ?? '',
        score: Math.round(Number((overAllResult as any)?.overall_score) ?? 0),
        eventId: careerPractice?.eventId ?? careerPractice?.id ?? '',
        eventType: 'INTERVIEWATHON',
      },
    });
  }
  const response = await db.careerPractice.update({
    data: {
      feedback: { ...overAllResult, ...(careerPractice as any)?.feedback },
      conversation: updatedConversation,
      timing: {
        ...(careerPractice as any)?.timing,
        feedBackGenerateTime: new Date(),
      },
      finalScore: Math.round((overAllResult as any)?.overall_score),
    },
    where: {
      id: id,
    },
  });
  try {
    await generateConsolidatedFeedback(id);
  } catch (error) {
    console.log('Error in generating consolidated feedback', error);
  }

  return NextResponse.json({ message: 'Feedback Generated' }, { status: 200 });
}

const generateVideoResult = async ({ systemInput, url, question, bucket }) => {
  // Use provided bucket or default
  const bucketName = bucket || defaultBucket;

  const config: VertexAIConfiguration = {
    project,
    model,
    location,
    systemInput,
    credentials: {
      private_key: process.env.NEXT_PUBLIC_PRIVATE_KEY?.split(String.raw`\n`).join('\n') || '',
      client_id: process.env.NEXT_PUBLIC_CLIENT_ID || '',
      client_email: process.env.NEXT_PUBLIC_CLIENT_EMAIL || '',
    },
    generationConfig: {
      maxOutputTokens: 8192,
      temperature: 0.5,
      topP: 0.5,
    },
  };
  // console.log({ config });

  const vertexAI = new GCPVertexAI(config);
  const stream = await vertexAI.generateContent({
    video: {
      fileData: {
        mimeType: 'video/mp4',
        fileUri: `gs://${bucketName}/${folder}/${url}`,
      },
    },
    question,
  });

  return stream;
};
