import { NextResponse } from 'next/server';

import base64 from 'base-64';

export const maxDuration = 299;

const dyteBaseURL = process.env.DYTE_BASE_URL;
const dyteOrgId = process.env.DYTE_ORGANIZATION_ID;
const dyteAPIKey = process.env.DYTE_API_KEY;
export async function POST(request: any) {
  const input = await request.json();
  const authorization = base64.encode(`${dyteOrgId}:${dyteAPIKey}`);
  let data;
  try {
    const response = await fetch(`${dyteBaseURL}/meetings`, {
      method: 'POST',
      body: JSON.stringify({
        title: input?.title,
        preferred_region: 'ap-south-1',
        record_on_start: true,
        recording_config: {
          max_seconds: 18000,
          file_name_prefix: 'dyte_video',
          video_config: {
            codec: 'H264',
            width: 1280,
            height: 720,
            export_file: true,
          },
          audio_config: {
            codec: 'AAC',
            channel: 'stereo',
            export_file: true,
          },
          storage_config: {
            type: 'aws',
            access_key: process.env.AWS_ACCESS_KEY_ID,
            secret: process.env.AWS_SECRET_ACCESS_KEY,
            bucket: process.env.NEXT_PUBLIC_S3BUCKET ?? 'aceprep-bucket',
            region: process.env.AWS_DEFAULT_REGION || 'ap-south-1',
            path: `${process.env.NEXT_PUBLIC_S3FOLDER}/video-meeting`,
          },
          dyte_bucket_config: {
            enabled: false,
          },
        },
      }),
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Basic ${authorization}`,
      },
    });
    data = await response?.json();

    if (data?.success) {
      return NextResponse.json(
        {
          id: data,
        },
        {
          status: 200,
        },
      );
    } else {
      return NextResponse.json({ error: 'Failed to create meeting' }, { status: 500 });
    }
  } catch (error) {
    console.error(error);
    return NextResponse.json(
      {
        id: data,
      },
      { status: 500 },
    );
  }
}
