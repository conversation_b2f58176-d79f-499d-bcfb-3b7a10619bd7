import { redirect } from 'next/navigation';

import { OnboardingInterviewScreen } from '@/components/onboarding-interview/interview-onboarding-screen';
import { authOptions } from '@/lib/auth';
import { getInterviewDetails, getUserDetails, getUserProfile } from '@/services/apicall';
import { getServerSession } from 'next-auth';

export default async function Onboarding(props) {
  const session = await getServerSession(authOptions);

  const id = props?.searchParams?.id;

  if (!session?.userId) {
    return redirect('/404');
  }

  const userProfile = await getUserProfile(session?.userId);

  const interviewDetails = await getInterviewDetails(id);

  if (!interviewDetails) {
    return redirect('/404');
  }

  const timing = interviewDetails?.result?.timing;

  if (interviewDetails?.result?.hasCompleted || timing?.completedTime) {
    return redirect(`/completed?id=${id}`);
  }

  let action;

  if (!userProfile) {
    action = 'update-profile';
  }

  return (
    <OnboardingInterviewScreen
      userProfile={userProfile}
      userId={session?.userId}
      session={session}
      interviewDetails={interviewDetails?.result}
      action={action}
    />
  );
}
